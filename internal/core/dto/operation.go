package dto

import (
	"ops-api/internal/core/domain"
	"strings"
)

type OperationCreateReq struct {
	ClusterID   uint64 `json:"cluster_id"`
	NamespaceID uint64 `json:"namespace_id"`
	Method      string `json:"method"`
}

type OperationData struct {
	Clusters Cluster `json:"clusters"`
}

type Cluster struct {
	ID            uint64      `json:"id"`
	Name          string      `json:"name"`
	Region        string      `json:"region"`
	NodePool      NodePool    `json:"node_pool"`
	LoadBalanceIP string      `json:"load_balance_ip"`
	Namespaces    []Namespace `json:"namespaces"`
}

type NodePool struct {
	PoolName  string `json:"pool_name"`
	Size      string `json:"size"`
	NodeCount uint64 `json:"node_count"`
}

type Namespace struct {
	ID          uint64       `json:"id"`
	Name        string       `json:"name"`
	Namespace   string       `json:"namespace"`
	IsActive    bool         `json:"is_active"`
	Deployments []Deployment `json:"deployments"`
	Services    []Service    `json:"services"`
	Ingress     []Ingress    `json:"ingress"`
}

type Deployment struct {
	ID   uint64         `json:"id"`
	Name string         `json:"name"`
	Spec DeploymentSpec `json:"spec"`
}

type DeploymentSpec struct {
	Container Container `json:"container"`
}

type Container struct {
	Image string         `json:"image"`
	Name  string         `json:"name"`
	Port  DeploymentPort `json:"port"`
	Env   []Env          `json:"env"`
}

type DeploymentPort struct {
	ContainerPort uint64 `json:"container_port"`
}

type Env struct {
	Name  string `json:"name"`
	Value string `json:"value"`
}

type Service struct {
	Name string      `json:"name"`
	Spec ServiceSpec `json:"spec"`
}

type ServiceSpec struct {
	Selector ServiceSelector `json:"selector"`
	Port     ServicePort     `json:"port"`
}

type ServiceSelector struct {
	App string `json:"app"`
}

type ServicePort struct {
	Port       string `json:"port"`
	TargetPort string `json:"target_port"`
}

type Ingress struct {
	Name    string        `json:"name"`
	ApiHost string        `json:"api_host"`
	Spec    []IngressSpec `json:"spec"`
}

type IngressSpec struct {
	Rule IngressRule `json:"rule"`
}

type IngressRule struct {
	Host string      `json:"host"`
	Http IngressHttp `json:"http"`
}

type IngressHttp struct {
	Path IngressPath `json:"path"`
}

type IngressPath struct {
	Path    string         `json:"path"`
	Backend IngressBackend `json:"backend"`
}

type IngressBackend struct {
	Service IngressService `json:"service"`
}

type IngressService struct {
	Name string             `json:"name"`
	Port IngressServicePort `json:"port"`
}

type IngressServicePort struct {
	Number uint64 `json:"number"`
}

type AgentRequest struct {
	SecretKey string `json:"secret_key"`
	LineCode  string `json:"line_code"`
}

func ToOperationDataDTO(c domain.Cluster, defaultDomain string, agent AgentRequest, apiHost string) OperationData {
	cluster := Cluster{
		ID:     c.ID,
		Name:   c.Name,
		Region: c.Region,
		NodePool: NodePool{
			PoolName:  c.PoolName,
			Size:      c.Size,
			NodeCount: c.NodeCount,
		},
		LoadBalanceIP: c.LoadBalanceIP,
		Namespaces: func() []Namespace {
			namespaces := make([]Namespace, len(c.Namespaces))
			for i_nsp, namespace := range c.Namespaces {
				namespaces[i_nsp] = Namespace{
					ID:        namespace.ID,
					Name:      namespace.Name,
					Namespace: namespace.Name,
					IsActive:  namespace.IsActive,
					Deployments: func() []Deployment {
						deployments := make([]Deployment, len(namespace.Deployments))
						for i_dpm, deployment := range namespace.Deployments {
							deployments[i_dpm] = Deployment{
								ID:   deployment.ID,
								Name: deployment.Name,
								Spec: DeploymentSpec{
									Container: Container{
										Image: deployment.Image,
										Name:  deployment.Name,
										Port: DeploymentPort{
											ContainerPort: deployment.ContainerPort,
										},
										Env: func() []Env {
											envs := make([]Env, len(deployment.Environments))
											for i_env, env := range deployment.Environments {
												value := env.Value
												if strings.Contains(value, "{{DOMAIN}}") {
													value = strings.ReplaceAll(value, "{{DOMAIN}}", defaultDomain)
												}
												if strings.Contains(value, "{{APP_NAME}}") {
													value = strings.ReplaceAll(value, "{{APP_NAME}}", namespace.Name)
												}
												if strings.Contains(value, "{{AGENT_SECRET_KEY}}") {
													value = strings.ReplaceAll(value, "{{AGENT_SECRET_KEY}}", agent.SecretKey)
												}
												if strings.Contains(value, "{{AGENT_LINE_CODE}}") {
													value = strings.ReplaceAll(value, "{{AGENT_LINE_CODE}}", agent.LineCode)
												}
												envs[i_env] = Env{
													Name:  env.Name,
													Value: value,
												}
											}
											return envs
										}(),
									},
								},
							}

						}
						return deployments
					}(),
					Services: func() []Service {
						services := make([]Service, len(namespace.Services))
						for i_sv, service := range namespace.Services {
							services[i_sv] = Service{
								Name: service.Name,
								Spec: ServiceSpec{
									Selector: ServiceSelector{
										App: service.Name,
									},
									Port: ServicePort{
										Port:       service.Port,
										TargetPort: service.TargetPort,
									},
								},
							}

						}
						return services
					}(),
					Ingress: func() []Ingress {
						ingress := make([]Ingress, len(namespace.Ingress))
						for i_ig, ig := range namespace.Ingress {
							ingress[i_ig] = Ingress{
								Name:    ig.Name,
								ApiHost: apiHost,
								Spec: func() []IngressSpec {
									specs := make([]IngressSpec, len(ig.IngressSpecs))
									for i_sp, spec := range ig.IngressSpecs {
										serviceName := ""
										if spec.Service != nil {
											serviceName = spec.Service.Name
										}
										specs[i_sp] = IngressSpec{
											Rule: IngressRule{
												Host: spec.Host,
												Http: IngressHttp{
													Path: IngressPath{
														Path: spec.Path,
														Backend: IngressBackend{
															Service: IngressService{
																Name: serviceName,
																Port: IngressServicePort{
																	Number: spec.Port,
																},
															},
														},
													},
												},
											},
										}
									}
									return specs
								}(),
							}
						}
						return ingress
					}(),
				}
			}
			return namespaces
		}(),
	}
	return OperationData{
		Clusters: cluster,
	}
}
